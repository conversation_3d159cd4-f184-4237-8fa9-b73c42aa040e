<script setup lang="ts">
import { reactive, ref, defineComponent, defineEmits } from 'vue'
import { cancelAccount } from "@/api/index.js"
import { ElMessage } from 'element-plus';
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import { useRouter } from 'vue-router';
const router = useRouter();
const props = defineProps({
    dialogVisibleConfirm: {
        type: Boolean,
        require: false,
        default: false
    }

})
// 定义组件的事件
const emits = defineEmits(['dialogCloseConfirm', 'confirmLogout'])
const handleClose = () => {
    // 触发自定义事件来通知父组件关闭弹窗
    emits('dialogCloseConfirm', false)
}
// 确认
const confirm = async () => {
    try {
            var query = {
                token:localStorage.getItem('token')
            }
            // console.log(query)
            const res = await cancelAccount(query);
            console.log(res)
            if (res.code == 0 || res.code == 1) {
                // 注销成功
                emits('confirmLogout', false)
                localStorage.clear();
                countStore.login = '';
                ElMessage({
                    message: 'Cancellation successful',
                    type: 'success'
                });
                router.back();
            } else {
                // 修改失败
                ElMessage({
                    message: 'Cancellation Fail',
                    type: 'warning'
                });
            }

    } catch (error) {
        console.error('Fetch error:', error);
    }
}
// 取消
const cancel = () => {
    emits('dialogCloseConfirm', false)
}

</script>

<template>
    <div>
        <el-dialog :model-value="props.dialogVisibleConfirm" title="" width="25%" align-center @close="handleClose">
            <div class="con">
                <div class="top">
                    <div class="title">Are you sure you want to cancel your application account?</div>
                </div>
                <div class="btn">
                    <el-button @click="confirm"> Sure</el-button>
                    <el-button color="rgba(33, 95, 215, 1)" @click="cancel"> Think again</el-button>
                </div>
            </div>

        </el-dialog>
    </div>
</template>
<style scoped lang="scss">
:deep(.el-dialog) {
    background: linear-gradient(231deg, #F4F9FF 0%, #EBF4FF 100%);
    border-radius: 18px;
}

:deep(.el-dialog__header) {
    text-align: center;
}

:deep(.el-dialog__body) {
    padding: 12px 0 0;
}

.con {
    display: flex;
    flex-direction: column;
    align-items: center;


    .top {
        display: flex;
        font-size: var(--fontSize22);
        margin-bottom: 30px;

        .title {
            font-weight: var(--fontWeight5);
            color: rgba(0, 0, 0, 0.8);
            line-height: 27px;
            text-align: center;
        }
    }

    .btn {
        margin: 30px 0 34px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        width: 80%;
    }

    :deep(.el-button) {
        height: 48px;
    }

}

@import "@/assets/css/media-queries.scss";
</style>
