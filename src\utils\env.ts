/**
 * Environment configuration interface
 */
interface EnvironmentConfig {
  loginUrl: string;
  copyUrl: string;
  redirectURI: string;
}

/**
 * Environment types
 */
type Environment = 'production' | 'pre' | 'test' | 'development';

/**
 * Get the current environment from Vite environment variables
 */
const getCurrentEnvironment = (): Environment => {
  const env = import.meta.env.VITE_APP_TITLE as string;

  if (env === 'production' || env === 'pre' || env === 'test') {
    return env as Environment;
  }

  return 'development';
};

/**
 * Environment configurations for different deployment stages
 */
const environmentConfigs: Record<Environment, EnvironmentConfig> = {
  production: {
    loginUrl: 'https://loginmeta.hapmetasocialltd.com:8010',
    copyUrl: 'https://www.hapmetasocialltd.com',
    redirectURI: 'https://www.hapmetasocialltd.com/index'
  },
  pre: {
    loginUrl: 'https://loginmetapre.hapmetasocialltd.com:6010',
    copyUrl: 'https://wwwpre.hapmetasocialltd.com',
    redirectURI: 'https://wwwpre.hapmetasocialltd.com/index'
  },
  test: {
    loginUrl: 'https://loginmetatest.hapmetasocialltd.com:6010',
    copyUrl: 'https://wwwtest.hapmetasocialltd.com',
    redirectURI: 'https://wwwtest.hapmetasocialltd.com/index'
  },
  development: {
    loginUrl: 'http://192.168.1.109:8010',
    copyUrl: 'https://wwwtest.hapmetasocialltd.com',
    redirectURI: 'https://bursting-cub-informed.ngrok-free.app/index'
  }
};

/**
 * Get the current environment configuration
 */
const getEnvironmentConfig = (): EnvironmentConfig => {
  const currentEnv = getCurrentEnvironment();
  return environmentConfigs[currentEnv];
};

/**
 * Current environment configuration
 */
const config: EnvironmentConfig = getEnvironmentConfig();

export default config;
export { type EnvironmentConfig, type Environment, getCurrentEnvironment, getEnvironmentConfig };