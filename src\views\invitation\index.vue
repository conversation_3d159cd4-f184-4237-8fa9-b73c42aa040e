<script setup lang="ts">
import { defineComponent, ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRouter } from 'vue-router';
const router = useRouter()
import Login from "@/components/invitation/login.vue"
import Forget from "@/components/invitation/forget.vue"
import Register from "@/components/invitation/register.vue"
import { showToast, showFailToast } from 'vant';
import { quitAccount, bindingList, login, tokenLogin, binding } from "@/api/index.js"
import config from '@/utils/env'
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import useClipboard from 'vue-clipboard3'
const { toClipboard } = useClipboard();
const dataList = ref([])
// [{ id: 0, title: 'ID:**********@.com' }, { id: 1, title: 'ID:**********@.com' }, { id: 2, title: 'ID:**********@.com' }, { id: 3, title: 'ID:**********@.com' }, { id: 4, title: 'ID:**********@.com' }, { id: 5, title: 'ID:**********@.com' }, { id: 6, title: 'ID:**********@.com' }, { id: 7, title: 'ID:**********@.com' }, { id: 8, title: 'ID:**********@.com' }, { id: 9, title: 'ID:**********@.com' }]
const aboutDialogVisible = ref(false)
const dialogVisibleForget = ref(false)
const dialogVisibleRegister = ref(false)
const showToastAccount = ref(false);
const login = ref(localStorage.getItem('id'))
import pingguo from '@/assets/images/index/pingguo 2.png'
import GooglePlay from '@/assets/images/index/a-GooglePlay 2.png'
import anzhuo from '@/assets/images/index/anzhuo 2.png'
const downloadList = [{ id: 0, url: pingguo, title: 'App store Download', color: '#141414' }, { id: 1, url: GooglePlay, title: 'Google play Download', color: '#56C366' }];
const downBtn = ((data:number) => {
  if (data == 0) {
    window.location.href = 'https://apps.apple.com/us/app/hapmeta-social/id6738371086'
  } else if (data == 1) {
    window.location.href = 'https://play.google.com/store/apps/details?id=com.geniusmetaltd.hapmetasocial'
  }
})
// 点击登录
const openDialog = (data: boolean) => {
  aboutDialogVisible.value = data
}
// 收到登录页面关闭
const dialogClose = (data: boolean) => {
  console.log(data)
  aboutDialogVisible.value = data
  login.value = localStorage.getItem('id')
}
// 点击忘记密码
const dialogForget = (data: boolean) => {
  aboutDialogVisible.value = false //登录页面是否关闭
  dialogVisibleForget.value = data
}
// 收到忘记密码页面关闭
const dialogCloseForget = (data: boolean) => {
  dialogVisibleForget.value = data
}
// 点击注册
const dialogRegister = (data: boolean) => {
  aboutDialogVisible.value = false //登录页面是否关闭
  dialogVisibleRegister.value = data
}
const dialogCloseRegister = (data: boolean) => {
  dialogVisibleRegister.value = data
}
// 收到去登录
const dialogLogin = (data: boolean) => {
  aboutDialogVisible.value = data
}
// 注销账号
const account = async () => {
  console.log('2222')
  // router.push({ path: '/index/deleteAccount' })
  try {
    // var query = {
    //   userId: localStorage.getItem('id')
    // }
    // console.log(query)
    const res = await quitAccount(localStorage.getItem('id'));
    console.log(res)
    if (res.code == 0 || res.code == 1) {
      // 注销成功
      showToastAccount.value = true
      localStorage.clear();
      login.value = ''
      setTimeout(() => {
        showToastAccount.value = false
      }, 5000);
    } else if (res.code == 5) {
      // 用户不存在
      showToast({
        message: 'User does not exist.',
      });
      localStorage.clear();
      login.value = ''
    } else {
      // 修改失败
      showToast({
        message: 'Cancellation failed',
      });
    }
  } catch (error) {
    console.error('Fetch error:', error);
  }
}
// 绑定列表
const bindingListBtn = async () => {

  try {
    const query = {
      bindingId: localStorage.getItem('id')
    }
    const res = await bindingList(query);
    console.log(res)
    if (res.code == 1) {
      dataList.value = res.bindingList
      console.log(dataList.value)
    } else {
      // 修改失败

    }
  } catch (error) {
    console.error('Fetch error:', error);
    showToast({ message: '获取绑定列表失败' })

  }
}
// 192.168.1.52:5173/invitation/?id=xxx
// 查看二维码
const seeCode = () => {
  router.push({ path: '/invitation/code' })
}
// 点击复制链接
const copyLink = () => {
  // 判断是否登录 没登陆提示先登录
  if (localStorage.getItem('id')) {
    console.log(config.copyUrl)
    const text = `${config.copyUrl}/invitation/?id=${localStorage.getItem('id')}`
    console.log(text)
    copyValue(text)
  } else {
    showToast({
      message: 'Please log in first',
    });
  }
}
// 浏览器禁用了非安全域的 navigator.clipboard 对象 安全域包括本地访问与开启TLS安全认证的地址，如 https 协议的地址、127.0.0.1 或 localhost 。
// 先给要复制的文本或者按钮加上点击事件后，并将要复制的值传过来
// const copyValue = async (val) => {
//   if (navigator.clipboard && window.isSecureContext) {
//     // navigator clipboard 向剪贴板写文本
//     showToast({ message: '复制成功' })
//     return navigator.clipboard.writeText(val)
//   } else {
//     // 创建text area
//     const textArea = document.createElement('textarea')
//     textArea.value = val
//     // 使text area不在viewport，同时设置不可见
//     document.body.appendChild(textArea)
//     textArea.focus()
//     textArea.select()
//     showToast({
//       message: '复制成功',
//     });
//     return new Promise((res, rej) => {
//       // 执行复制命令并移除文本框
//       document.execCommand('copy') ? res() : rej()
//       textArea.remove()
//     })
//   }
// }
const copyValue = async (val) => {
  try {
    await toClipboard(val);
    console.log('文本已复制到剪贴板');
    showToast({ message: 'Copy successful' })
  } catch (error) {
    console.error('复制失败', error);
    showToast({ message: 'Copy failed' })
  }
}
// 点击logo跳转首页
const gotoDetail = () => {
  // router.push({ path: '/index' })
}
const loginBtn = async (token:string, userId:string) => {  // 表单输入完毕后点登录调用handleFinish函数
  try {
    var query = {
      token,
      userId
    }
    console.log(query)
    const res = await tokenLogin(query);
    console.log(res)
    if (res.code == 7) {
      // 登录成功
      localStorage.setItem('id', res.userId);
      localStorage.setItem('name', res.name)
      login.value = res.userId
      showToast('Login successful');
    } else if (res.code == 11) {
      // 登录失败
      showFailToast('token或id不正确')
    } else {
      showFailToast('Login failed')
    }

  } catch (error) {
    console.error('Fetch error:', error);
  }
}
const GetRequest = () => {
  const url = window.top.location.href;
  const params = {};
  url.replace(/[?&]+([^=&]+)=([^&]*)/gi, function (m, key, value) {
    params[key] = decodeURIComponent(value);
  });
  console.log(params); // 输出包含查询参数键值对组成的对象
  if (params.token) {
    // 传的是账号密码 需自动登录 自己的账户
    // 调登录方法
    loginBtn(params.token, params.id)
    countStore.id = params.id
    localStorage.setItem('token', params.token);
  } else if (params.id) {
    // 别人打开链接
    countStore.id = params.id
    console.log('打开窗口')
    dialogRegister(true)
  }
  //  console.log(param1, param2); // 输出：value1 value2
}
const bindingSuccess = () => {
  bindingListBtn()
}
watch(() => login.value, (n, o) => {
  console.log(n)
})
onMounted(() => {
  // 获取url传参
  GetRequest()
  // console.log()
  if (login.value) {
    bindingListBtn()
  }
  // setTimeout(() => {
  //       showToastAccount.value = false
  //     }, 5000);
  // showSuccessToast('奖励已在24小时内发送至您的账号邮箱');
  // isSeleteUrl.value=isSeleteAgree.noselete
})
</script>

<template>
  <div class="container">
    <div class="headMenu">
      <div class="top">
        <div v-if="login" class="notLogin">
          <div class="name">{{ login }}</div>
          <div class="cancelAccount" @click="account()">Cancellation</div>
        </div>
        <div v-else class="login" @click="openDialog(true)">Login</div>
        <img src="@/assets/images/active/logo.png" alt="" @click="gotoDetail">
      </div>
      <div class="bgText">Invite your friends <br>to get rewards</div>

      <div class="download-list">
        <div class="download-item" v-for="item in downloadList" :key="item.id" :style="{ background: item.color }"
          :class="[item.id == 1 ? 'otherMargin' : '']" @click="downBtn(item.id)">
          <img :src="item.url">
          <div class="text">{{ item.title }}</div>
        </div>

      </div>

    </div>
    <div class="bottomCon">
      <div class="centerBtn">
        <div class="left-btn" @click="seeCode">Save QR code</div>
        <div class="right-btn" @click="copyLink">Copy invitation link</div>
      </div>
      <div class="rule">
        <div class="title"></div>
        <div class="con"> For every new invited user who downloads "Hapgeta Social" and successfully registered, the
          inviter will receive
          <span>1000 time-space coins</span>
        </div>
      </div>
      <div class="tnfsi ">
        <div class="title"></div>
        <div class="con">
          <div class="con-item" v-for="(item, index) in dataList" :key="item.index">ID:{{ item }}</div>
        </div>
      </div>
    </div>

  </div>
  <Login :aboutDialogVisible="aboutDialogVisible" @dialogClose="dialogClose" @dialogForget="dialogForget"
    @dialogRegister="dialogRegister" @bindingSuccess="bindingSuccess" />
  <Forget :dialogVisibleForget="dialogVisibleForget" @dialogCloseForget="dialogCloseForget" />
  <Register :dialogVisibleRegister="dialogVisibleRegister" @dialogCloseRegister="dialogCloseRegister"
    @dialogLogin="dialogLogin"></Register>
  <!-- 注销成功 -->
  <van-toast v-model:show="showToastAccount" style="padding: 0">
    <template #message>
      <div class="toastCon">
        <img src="@/assets/images/active/success.png" alt="">
        <div class="title" style="margin-bottom: 54px">Cancellation successful</div>
      </div>
    </template>
  </van-toast>
</template>


<style scoped lang="scss">
@import "@/assets/css/login/toast.scss";

* {
  padding: 0;
  margin: 0;
}

.container {
  height: 100%;
  // background: url('@/assets/images/active/bg (1).png') no-repeat;
  // background-size: 100% 100%;
  // display: flex;
  // flex-direction: column;
  // align-items: center;

  .headMenu {
    width: 100%;
    height: 609px;
    max-height: 1396px;
    // height: 45%;
    background: url('@/assets/images/active/bg (4).png') no-repeat;
    background-size: 100% 100%;
    position: relative;

    // background-size: cover;
    // background-position: center;
    .top {
      display: flex;
      // margin:7px 46px 0 48px ; 
      padding-top: 7px;
      margin-right: 46px;
      margin-left: 48px;
      color: rgba(255, 255, 255, 0.7);
      font-size: 26px;
      position: relative;

      .login {
        margin-top: 7px;
        flex: 1;
      }

      .notLogin {
        display: flex;
        margin-top: 7px;
        flex: 1;
        align-items: center;
        font-size: 20px;

        .name {
          padding-right: 14px;
        }
      }

      img {
        width: 240px;
        height: 50px;
      }
    }

    .bgText {
      position: absolute;
      top: 104px;
      text-align: center;
      width: 100%;
      font-size: 68px;
      font-weight: 600;
    }

    .download-list {
      position: absolute;
      bottom: 5px;
      left: 50%;
      transform: translateX(-50%);
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 14px;

      .download-item {
        width: 180px;
        height: 58px;
        background: #141414;
        border-radius: 8px;
        display: flex;
        align-items: center;

        img {
          margin: 0 10px 0 20px;
          width: 34px;
          height: 34px;
        }

        .text {
          font-size: 18px;
          color: #FFFFFF;
          line-height: 24px;
        }
      }

      .otherMargin {
        img {
          margin: 0 7px 0 14px;
        }
      }
    }

  }

  .bottomCon {
    height: 1122px;
    width: 100%;
    background: url('@/assets/images/active/bg (3).png') no-repeat;
    background-size: 100% 100%;
    margin-top: -45px;
    padding-bottom: 45px;

    .centerBtn {
      padding: 74px 78px 36px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 22px;
      text-align: center;
      height: 109px;
      line-height: 109px;
      font-size: 22px;
      font-family: FZHZGBJW--GB1, FZHZGBJW--GB1;
      font-weight: normal;

      .left-btn {
        width: 100%;
        background: url('@/assets/images/active/lanBtn.png') no-repeat;
        background-size: 100% 100%;
      }

      .right-btn {
        width: 100%;
        background: url('@/assets/images/active/huangBtn.png') no-repeat;
        background-size: 100% 100%;
      }

    }

    .rule {
      padding-right: 50px;
      padding-left: 50px;
      margin-right: 30px;
      margin-left: 30px;
      margin-bottom: 37px;
      font-size: 28px;
      font-family: SourceHanSansSC, SourceHanSansSC;
      font-weight: 400;
      color: #92F6FF;
      line-height: 50px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: url('@/assets/images/active/ruleBg.png') no-repeat;
      background-size: 100% 100%;
      z-index: 222;

      .title {
        width: 496px;
        height: 42px;
        background: url('@/assets/images/active/rule.png') no-repeat;
        background-size: 100% 100%;
        margin-bottom: 10px;
        margin-top: 53px;
      }

      .con {
        // width: 589px;
        margin-bottom: 40px;
      }

      span {
        color: rgba(253, 203, 30, 1);
      }
    }

    .tnfsi {
      padding-right: 50px;
      padding-left: 50px;
      margin-right: 30px;
      margin-left: 30px;
      margin-bottom: 37px;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: url('@/assets/images/active/ruleBg.png') no-repeat;
      background-size: 100% 100%;
      z-index: 222;
      min-height: 406px;

      .title {
        height: 42px;
        width: 611px;
        background: url('@/assets/images/active/tnfsi.png') no-repeat;
        background-size: 100% 100%;
        margin-bottom: 10px;
        margin-top: 53px
      }

      .con {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        justify-content: space-between;
        grid-gap: 22px;
        font-size: 24px;
        font-family: SourceHanSansSC, SourceHanSansSC;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 35px;
      }
    }
  }
}

@import "@/assets/css/media-queries.scss";
</style>