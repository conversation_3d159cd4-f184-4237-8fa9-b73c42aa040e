<script setup lang="ts">
import { reactive, ref, defineComponent, defineEmits } from 'vue'
import { getCode, forgetPassword } from "@/api/index.js"
import { emailRules, passwordRules, codeRules } from "@/utils/rules.ts"
import { showLoadingToast, showToast, showFailToast } from 'vant';
import { useRouter } from 'vue-router';
const router = useRouter()
import debounce from 'lodash/debounce';
const loginFormRef = ref();
const props = defineProps({
    dialogVisibleForget: {
        type: Boolean,
        require: false,
        default: false
    }
})
// 定义组件的事件
const emits = defineEmits(['dialogCloseForget'])
const handleClose = () => {
    // 触发自定义事件来通知父组件关闭弹窗
    emits('dialogCloseForget', false)
    userInfo.email = ''
    userInfo.password = ''
    userInfo.code = ''
}
const name = ref('')
const userInfo = reactive({
    email: '',
    password: '',
    code: ''
})
// 获取验证码
let intervalId;
const showCode = ref(true)
let num = ref(0)
const getVerificationCode = debounce(() => {
    console.log('防抖触发点击事件');
    showCode.value = false
    code()
    // 设置60秒倒计时
    const count = 60;
    num.value = count;
}, 300);
const code = async () => {
    // 发送获取验证码的请求逻辑
    // 这里只是示例，你需要根据实际API来编写
    // 假设API返回成功表示可以发送验证码
    try {
        const query = {
            email: userInfo.email
        }
        const res = await getCode(query);
        console.log(res)
        if (res.code == 3) {
            showCode.value = true
            showToast({
                message: 'Please check if the email format is correct',
            });
        } else if (res.code == 2) {
            showCode.value = true
            showToast({
                message: 'The email cannot be empty',
            });
        } else if (res.code == 0) {
            intervalId = setInterval(() => {
                if (num.value <= 0) {
                    clearInterval(intervalId);
                    showCode.value = true
                } else {
                    num.value--;
                }
            }, 1000);
        } else if (res.code == 12) {
            showCode.value = true
            showToast('Email verification code sending failed ')
        } else {
            showCode.value = true
            showFailToast('Failed to get the verification code')
        }


    } catch (error) {
        showToast({
            message: 'Failed to get the verification code',
        });
    }
};
// 表单验证
const onSubmit = (values) => {
    loginFormRef.value?.validate().then(() => {
        // 验证通过
        forgetPasswordBtn()
    }).catch((err) => {
        //验证失败
        showFailToast('Please fill in the information correctly');
    })
};
// 忘记密码 确认修改
const forgetPasswordBtn = async () => {  // 表单输入完毕后点登录调用handleFinish函数
    try {
        var query = {
            email: userInfo.email,
            passWord: userInfo.password,
            code: userInfo.code
        }
        console.log(query)
        const res = await forgetPassword(query);
        console.log(res)
        if (res.code == 0) {
            // 修改成功
            showToast({
                message: 'Modification successful'
            });
            emits('dialogCloseForget', false)
        } else {
            // 修改失败
            showFailToast(res.msg)
        }
    } catch (error) {
        console.error('Fetch error:', error);
    }
}
</script>

<template>
    <div>
        <van-dialog v-model:show="props.dialogVisibleForget" title="Forgot password" align-center
            :show-confirm-button="false">
            <div class="con">
                <div class="close" @click="handleClose">
                    <img src="@/assets/images/active/close.png" alt="">
                </div>
                <van-form ref="loginFormRef">
                    <van-field v-model="userInfo.email" placeholder="Please enter your email " :rules="emailRules" />
                    <van-field v-model="userInfo.code" placeholder="Please enter the verification code"
                        :rules="codeRules">
                        <template #button>
                            <div class="verificationCode">
                                <div class="split"></div>
                                <div class="code" @click="getVerificationCode" v-if="showCode">Get verification code
                                </div>
                                <div class="code" v-else>{{ num }}</div>
                            </div>
                        </template>
                    </van-field>
                    <van-field v-model="userInfo.password" placeholder="Please enter a new password" type="password" show-password
                        :rules="passwordRules" />
                </van-form>
                <div class="login" @click="onSubmit">Confirm modification</div>
            </div>

        </van-dialog>
    </div>
</template>

<style scoped lang="scss">
@import "@/assets/css/login/dialog.css";
@import "@/assets/css/login/index.scss";
.login{
    margin-bottom: 20px;
}
</style>
