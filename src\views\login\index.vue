<script setup lang="ts">
import { reactive, ref, defineComponent, defineEmits, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { login, emailLogin, binding, getGoogleLogin, getAppleLogin } from "@/api/index.js"
import { emailRules, passwordRules } from "@/utils/rules.ts"
import { showLoadingToast, showToast, showFailToast } from 'vant';
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import Agreement from "@/components/agreement.vue"
import axios from 'axios';
import { Buffer } from 'buffer'; // 导入Buffer
import { useRouter } from 'vue-router'
const router = useRouter()
import config from '@/utils/env'
const loginFormRef = ref<FormInstance>()

const props = defineProps({
    dialogVisible: {
        type: Boolean,
        require: false,
        default: false
    }

})
const isSelete = ref(false)
// 定义组件的事件
const emits = defineEmits(['dialogClose', 'dialogForget'])
const handleClose = () => {
    // 触发自定义事件来通知父组件关闭弹窗
    emits('dialogClose', false)
    userInfo.email = ''
    userInfo.password = ''
    isSelete.value = false

}
const name = ref('')
const userInfo = reactive({
    email: '',
    password: ''
})
const rules = reactive<FormRules<typeof ruleForm>>({
    password: passwordRules,
    email: emailRules,
})
const forget = () => {
    emits('dialogForget', true)


}
const changeData = (data: boolean) => {
    isSelete.value = data
}
// 表单验证
const onSubmit = (values: any) => {
    loginFormRef.value?.validate().then(() => {
        // 验证通过
        loginBtn()
    }).catch((err:any) => {
        //验证失败
        ElMessage('Please fill in the information correctly');
    })
};
const loginBtn = async () => {  // 表单输入完毕后点登录调用handleFinish函数
    try {
        if (isSelete.value) {
            var query = {
                email: userInfo.email,
                passWord: userInfo.password
            }
            console.log(query)
            const res = await emailLogin(query);
            console.log(res)
            if (res.code == 7) {
                // 登录成功
                localStorage.setItem('id', res.userId);
                localStorage.setItem('name', res.name)
                countStore.login = localStorage.getItem('id')
                if (res.token) {
                    localStorage.setItem('token', res.token);
                }
                ElMessage.success('Login successful');
                emits('dialogClose', false)
                // 判断是否已绑定

            } else if (res.code == 5) {
                // 登录失败
                ElMessage.error('Email or password is incorrect')
            } else {
                ElMessage.error('Login failed')
            }
        } else {
            ElMessage.error('Please check the agreement firs')
        }
    } catch (error) {
        console.error('Fetch error:', error);
    }

}

import { decodeCredential, googleAuthCodeLogin, googleTokenLogin } from 'vue3-google-login'
const callback = (response: any) => {
    // This callback will be triggered when the user selects or login to
    // his Google account from the popup
    console.log("Handle the response", response)
    // decodeCredential will retrive the JWT payload from the credential
    const userData = decodeCredential(response.credential)
    console.log("Handle the userData", userData)
}
const googleLogin = () => {
    googleTokenLogin().then((response) => {
        console.log("Handle the response", response)
        googleLoginBtn(response.access_token)
    })
}
const googleLoginBtn = async (data: string) => {  // 表单输入完毕后点登录调用handleFinish函数
    try {
        if (isSelete.value) {
            // var accessToken = "*************************************************************************************************************************************************************************************************************************"
            // console.log(accessToken)

            const res = await getGoogleLogin(data);
            console.log(res)
            if (res.code == 7) {
                // 登录成功
                localStorage.setItem('id', res.account.userId);
                localStorage.setItem('name', res.account.username)
                countStore.login = localStorage.getItem('id')
                if (res.token) {
                    localStorage.setItem('token', res.token);
                }
                ElMessage.success('Login successful');
                console.log('chengg')
                emits('dialogClose', false)

            } else if (res.code == 5) {
                // 登录失败
                ElMessage.error('Email or password is incorrect')
            } else if (res.code == 13) {
                // token已过期
                ElMessage.error('The token has expired')
            } else {
                ElMessage.error('Login failed')
            }
        } else {
            ElMessage.error('Please check the agreement firs')
        }
    } catch (error) {
        console.error('Fetch error:', error);
    }
}
onMounted(() => {
    // 初始化 Apple Sign-In
    window.AppleID.auth.init({
        clientId: 'com.geniusmetaltd.hapmetasocial.server', // 替换为你的客户端 ID
        scope: 'email', // 需要的权限范围
        redirectURI: config.redirectURI, // 替换为你的重定向 URI
        state: 'origin:web', // 状态参数
        usePopup: true, // 使用弹出窗口进行登录
    })
})
const appleSignIn = async () => {
    try {
        const data = await window.AppleID.auth.signIn();
        console.log('Apple Sign-In successful:', data);
        // http://192.168.1.109:8088/login?
        // 获取 ID token
        const authorization = data.authorization;
        appleLoginBtn(data.authorization.id_token)
        // 调用你的 store action 来处理 Apple 登录
        // this.$store.dispatch('handleAppleLogin', authorization);
    } catch (error) {
        console.error('Apple Sign-In failed:', error);
    }
};
const appleLoginBtn = async (data: string) => {  // 表单输入完毕后点登录调用handleFinish函数
    try {
        if (isSelete.value) {
            const res = await getAppleLogin(data);
            console.log(res)
            if (res.code == 7) {
                // 登录成功
                localStorage.setItem('id', res.account.userId);
                localStorage.setItem('name', res.account.username)
                countStore.login = localStorage.getItem('id')
                if (res.token) {
                    localStorage.setItem('token', res.token);
                }
                ElMessage.success('Login successful');
                console.log('chengg')
                emits('dialogClose', false)

            } else if (res.code == 5) {
                // 登录失败
                ElMessage.error('Email or password is incorrect')
            } else if (res.code == 13) {
                // token已过期
                ElMessage.error('The token has expired')
            } else {
                ElMessage.error('Login failed')
            }
        } else {
            ElMessage.error('Please check the agreement firs')
        }
    } catch (error) {
        console.error('Fetch error:', error);
    }
}
</script>

<template>
    <div>
        <el-dialog class="loginDialog" :model-value="props.dialogVisible" title="Login" width="30%" align-center
            @close="handleClose">
            <div class="con">
                <el-form ref="loginFormRef" :rules="rules" :model="userInfo">
                    <el-form-item prop="email">
                        <el-input v-model="userInfo.email" placeholder="Please enter your email address" />
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input v-model="userInfo.password" placeholder="Please enter your password" show-password />
                    </el-form-item>
                    <div class="prompt">
                        <!-- <div class="info">请输入正确的密码</div> -->
                        <div class="forget" @click="forget">Forgot password</div>
                    </div>
                    <div class="login" @click="onSubmit">Login</div>
                    <div class="other">
                        <div class="other-top">
                            <div class="bar"></div>
                            <div class="text">Other</div>
                            <div class="bar"></div>
                        </div>
                        <div class="other-list">
                            <img @click="googleLogin" src="@/assets/images/login/google.png" />
                            <!--<img @click="loginWithTwitter" src="@/assets/images/login/twitter.png" />-->
                            <img @click="appleSignIn" src="@/assets/images/login/apple.png" />
                        </div>
                        <Agreement @changeData="changeData" @hideLogin="handleClose"></Agreement>
                        <!-- <GoogleLogin :callback="callback"></GoogleLogin> -->
                    </div>
                </el-form>

            </div>

        </el-dialog>
    </div>
</template>
<style scoped lang="scss">
@import "@/assets/css/login/dialog.css";

.con {
    margin: 0 64px;
    font-size: 16px;

    .prompt {
        display: flex;
        justify-content: space-between;
        margin: 10px 0;

        .info {
            color: #FF2B2B;
        }

        .forget {
            color: #3E4F75;
        }
    }

    .login {
        color: rgba(1, 1, 1, 0.7);
        background: url('@/assets/images/login/loginBtnBg.png') no-repeat;
        background-size: 100% 100%;
        height: 54px;
        line-height: 54px;
        text-align: center;
    }

    .other {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .other-top {
            margin: 9px 0 22px;
            display: flex;
            justify-content: center;
            align-items: center;

            .bar {
                width: 104px;
                height: 1px;
                border: 1px solid #899FC6;
            }

            .text {
                margin: 0 10px;
                font-size: var(--fontSize14);
                color: rgba(1, 2, 3, 0.74);
                line-height: 20px;
            }
        }

        .other-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 70px;

        }
    }
}
</style>
