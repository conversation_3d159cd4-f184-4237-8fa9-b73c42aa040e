<script setup lang="ts">
import { reactive, ref, defineComponent, defineEmits, onMounted } from 'vue'
import { login, emailLogin, binding, isBinding, getGoogleLogin, getAppleLogin } from "@/api/index.js"
import { emailRules, passwordRules } from "@/utils/rules.ts"
import { showLoadingToast, showToast, showFailToast } from 'vant';
import { useRouter } from 'vue-router';
const router = useRouter()
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import axios from 'axios';
import Agreement from "@/components/agreement.vue"
import config from '@/utils/env.js'
const isSelete = ref(false)
const loginFormRef = ref();
const showToastBinding = ref(false);
const props = defineProps({
    aboutDialogVisible: {
        type: Boolean,
        require: false,
        default: false
    }
})
// 定义组件的事件
const emits = defineEmits(['dialogClose', 'dialogForget', 'dialogRegister', 'bindingSuccess'])
const handleClose = () => {
    console.log('ssss')
    // 触发自定义事件来通知父组件关闭弹窗
    emits('dialogClose', false)
    userInfo.email = ''
    userInfo.password = ''
    isSelete.value = false
}
const name = ref('')
const userInfo = reactive({
    email: '',
    password: '',
})
// 去忘记密码
const forget = () => {
    emits('dialogForget', true)

}
// 去注册
const register = () => {
    emits('dialogRegister', true)

}
const changeData = (data: boolean) => {
    isSelete.value = data
}
// 确认登录
// const login = () => {
//     console.log('asss')
//     // var query = {
//     //     username: '卡死123',
//     //     password: '123456'
//     // }
//     // login(query)
//     //     .then((res) => {
//     //         // 接口调用成功之后的操作
//     //         console.log(res)
//     //     })
//     //     .catch((err) => {
//     //         // 接口调用失败之后的操作
//     //         console.log(err)
//     //     })
// }
// 表单验证
const onSubmit = (values: any) => {
    loginFormRef.value?.validate().then(() => {
        // 验证通过
        loginBtn()
    }).catch((err: any) => {
        //验证失败
        showFailToast('Please fill in the information correctly');
    })
};
const loginBtn = async () => {  // 表单输入完毕后点登录调用handleFinish函数
    try {
        if (isSelete.value) {
            var query = {
                email: userInfo.email,
                passWord: userInfo.password
            }
            console.log(query)
            const res = await emailLogin(query);
            console.log(res)
            if (res.code == 7) {
                // 登录成功
                localStorage.setItem('id', res.userId);
                localStorage.setItem('name', res.name)
                if (res.token) {
                    localStorage.setItem('token', res.token);
                }
                // 判断是否已绑定
                isBindingBtn(res.userId)
                emits('dialogClose', false)
            } else if (res.code == 5) {
                // 登录失败
                showFailToast('Email or password is incorrect')
            } else {
                showFailToast('Login failed')
            }
        } else {
            showToast({
                message: 'Please check the agreement first',
            });
        }

    } catch (error) {
        console.error('Fetch error:', error);
    }
}
const isBindingBtn = async (data) => {
    try {
        const res = await isBinding(data);
        console.log(res)
        if (res) {
            // 已绑定
            showToast('Login successful');
            emits('bindingSuccess')
        } else {
            // 调用绑定接口 如果已绑定 
            bindingBtn()
        }
    } catch (error) {
        console.error('Fetch error:', error);
        showFailToast('Network error')
    }
}
// 绑定
const bindingBtn = async () => {
    try {
        var query = {
            bindingId: countStore.id,   //    url传递的绑定信息 userId  
            byBindingId: localStorage.getItem('id') //    自己登录的id
        }
        console.log(query)
        const res = await binding(query);
        console.log(res)
        if (res.code == 200) {
            // 绑定成功
            showToastBinding.value = true
            setTimeout(() => {
                showToastBinding.value = false
            }, 5000);
            emits('bindingSuccess')
        } else if (res.code == 15) {
            showFailToast('Cannot bind oneself')
        }
        else if (res.code == 500) {
            // 登录失败
            showFailToast('The bound account does not exist')
        } else if (res.code == 5) {
            // 绑定失败，帐户已绑定
            showFailToast('Binding failed, the account is already bound')
        }
    } catch (error) {
        console.error('Fetch error:', error);
        showFailToast('Network error')
    }
}
import { decodeCredential, googleAuthCodeLogin, googleTokenLogin } from 'vue3-google-login'
const callback = (response) => {
    // This callback will be triggered when the user selects or login to
    // his Google account from the popup
    console.log("Handle the response", response)
    // decodeCredential will retrive the JWT payload from the credential
    const userData = decodeCredential(response.credential)
    console.log("Handle the userData", userData)
}
const googleLogin = () => {
    googleTokenLogin().then((response) => {
        console.log("Handle the response", response)
        googleLoginBtn(response.access_token)
    })
}
const googleLoginBtn = async (data: string) => {  // 表单输入完毕后点登录调用handleFinish函数
    try {
        if (isSelete.value) {
            // var accessToken = "*************************************************************************************************************************************************************************************************************************"
            // console.log(accessToken)
            const res = await getGoogleLogin(data);
            console.log(res)
            if (res.code == 7) {
                // 登录成功
                localStorage.setItem('id', res.account.userId);
                localStorage.setItem('name', res.account.username)
                if (res.token) {
                    localStorage.setItem('token', res.token);
                }
                // 判断是否已绑定
                isBindingBtn(res.account.userId)
                emits('dialogClose', false)
            } else if (res.code == 5) {
                // 登录失败
                showFailToast('Email or password is incorrect')
            } else if (res.code == 13) {
                // token已过期
                ElMessage.error('The token has expired')
            } else {
                showFailToast('Login failed')
            }
        } else {
            showToast({
                message: 'Please check the agreement first',
            });
        }
    } catch (error) {
        console.error('Fetch error:', error);
    }
}
onMounted(() => {
    // 初始化 Apple Sign-In
    window.AppleID.auth.init({
        clientId: 'com.geniusmetaltd.hapmetasocial.server', // 替换为你的客户端 ID
        scope: 'email', // 需要的权限范围
        redirectURI: config.redirectURI, // 替换为你的重定向 URI
        state: 'origin:web', // 状态参数
        usePopup: true, // 使用弹出窗口进行登录
    })
})
const appleSignIn = async () => {
    try {
        const data = await window.AppleID.auth.signIn();
        console.log('Apple Sign-In successful:', data);
        // http://192.168.1.109:8088/login?
        // 获取 ID token
        const authorization = data.authorization;
        appleLoginBtn(data.authorization.id_token)
        // 调用你的 store action 来处理 Apple 登录
        // this.$store.dispatch('handleAppleLogin', authorization);
    } catch (error) {
        console.error('Apple Sign-In failed:', error);
    }
};
const appleLoginBtn = async (data) => {  // 表单输入完毕后点登录调用handleFinish函数
    try {
        if (isSelete.value) {
            const res = await getAppleLogin(data);
            console.log(res)
            if (res.code == 7) {
                // 登录成功
                localStorage.setItem('id', res.account.userId);
                localStorage.setItem('name', res.account.username)
                if (res.token) {
                    localStorage.setItem('token', res.token);
                }
                ElMessage.success('Login successful');
                console.log('chengg')
                emits('dialogClose', false)

            } else if (res.code == 5) {
                // 登录失败
                ElMessage.error('Email or password is incorrect')
            } else if (res.code == 13) {
                // token已过期
                ElMessage.error('The token has expired')
            } else {
                ElMessage.error('Login failed')
            }
        } else {
            ElMessage.error('Please check the agreement firs')
        }
    } catch (error) {
        console.error('Fetch error:', error);
    }
}
</script>

<template>
    <div>
        <van-dialog v-model:show="props.aboutDialogVisible" title="Login" align-center :show-confirm-button="false">
            <div class="con">
                <div class="close" @click="handleClose">
                    <img src="@/assets/images/active/close.png" alt="">
                </div>
                <van-form ref="loginFormRef">
                    <van-field v-model="userInfo.email" placeholder="Please enter your email " :rules="emailRules" />
                    <van-field v-model="userInfo.password" placeholder="Please enter your password" type="password"
                        show-password :rules="passwordRules" />
                    <!-- <div class="info" >请输入正确的密码</div> -->
                </van-form>
                <div class="prompt">
                    <div class="forget" @click="register">Go register</div>
                    <div class="forget" @click="forget">Forgot password</div>
                </div>
                <div class="login" @click="onSubmit">Login</div>
                <div class="other">
                    <div class="other-top">
                        <div class="bar"></div>
                        <div class="text">Other</div>
                        <div class="bar"></div>
                    </div>
                    <div class="other-list">
                        <img @click="googleLogin" src="@/assets/images/login/google.png" />
                        <!-- <img src="@/assets/images/login/twitter.png" /> -->
                        <img @click="appleSignIn" src="@/assets/images/login/apple.png" />
                    </div>
                </div>
                <Agreement @changeData="changeData" @hideLogin="handleClose"></Agreement>
            </div>
        </van-dialog>
        <!-- 绑定成功提示 -->
        <van-toast v-model:show="showToastBinding" style="padding: 0">
            <template #message>
                <div class="toastCon">
                    <img src="@/assets/images/active/success.png" alt="">
                    <div class="title">Binding successful</div>
                </div>
            </template>
        </van-toast>

    </div>
</template>
<style scoped lang="scss">
@import "@/assets/css/login/dialog.css";
@import "@/assets/css/login/index.scss";
@import "@/assets/css/login/toast.scss";
</style>
